import { SOPNote } from '@hakimo-ui/hakimo/types';
import { Button, InputField, Checkbox } from '@hakimo-ui/shared/ui-base';
import { TrashIcon } from '@heroicons/react/24/outline';

interface Props {
  items: SOPNote[];
  onChange: (val: SOPNote[]) => void;
  showDeleteButtons?: boolean;
  showAddButton?: boolean;
}

export function WorkflowFormNotes(props: Props) {
  const {
    items,
    onChange,
    showDeleteButtons = true,
    showAddButton = true,
  } = props;

  const onInputChange =
    (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      const updatedItems = [...items];
      updatedItems[index] = {
        ...updatedItems[index],
        text: val
      };
      onChange(updatedItems);
    };

  const onCheckboxChange = (index: number) => (checked: boolean) => {
    const updatedItems = [...items];
    updatedItems[index] = {
      ...updatedItems[index],
      isEnabledForAIOperator: checked
    };
    onChange(updatedItems);
  };

  const onDeleteItem = (index: number) => () => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    onChange(updatedItems);
  };

  const onAddItem = () => {
    const updatedItems = [...items];
    updatedItems.push({
      text: '',
      isEnabledForAIOperator: false
    });
    onChange(updatedItems);
  };

  return (
    <div className="col-span-2 space-y-4">
      {items.map((item, i) => (
        <div key={i} className="flex items-center gap-4">
          <div className="flex-1">
            <InputField
              value={item.text}
              type="text"
              onChange={onInputChange(i)}
              placeholder="Enter note text..."
            />
          </div>
          <Checkbox
            checked={item.isEnabledForAIOperator}
            onChange={onCheckboxChange(i)}
            title="Enable for AI Operator"
          />
          {showDeleteButtons && (
            <Button variant="icon" onClick={onDeleteItem(i)}>
              <TrashIcon className="h-5 w-5" />
            </Button>
          )}
        </div>
      ))}
      {showAddButton && (
        <div>
          <Button onClick={onAddItem}>Add note</Button>
        </div>
      )}
    </div>
  );
}
