import { SOPNote } from '@hakimo-ui/hakimo/types';
import { Button, InputField, Checkbox } from '@hakimo-ui/shared/ui-base';
import { TrashIcon } from '@heroicons/react/24/outline';

interface Props {
  items: string[] | SOPNote[];
  onChange: (val: SOPNote[]) => void;
  showDeleteButtons?: boolean;
  showAddButton?: boolean;
}

// Helper function to convert string[] to SOPNote[]
const convertToSOPNotes = (items: string[] | SOPNote[]): SOPNote[] => {
  if (items.length === 0) return [];
  
  // Check if it's already SOPNote[]
  if (typeof items[0] === 'object' && 'text' in items[0]) {
    return items as SOPNote[];
  }
  
  // Convert string[] to SOPNote[]
  return (items as string[]).map(text => ({
    text,
    isEnabledForAIOperator: false
  }));
};

export function WorkflowFormNotes(props: Props) {
  const {
    items,
    onChange,
    showDeleteButtons = true,
    showAddButton = true,
  } = props;

  const sopNotes = convertToSOPNotes(items);

  const onInputChange =
    (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      const updatedItems = [...sopNotes];
      updatedItems[index] = {
        ...updatedItems[index],
        text: val
      };
      onChange(updatedItems);
    };

  const onCheckboxChange = (index: number) => (checked: boolean) => {
    const updatedItems = [...sopNotes];
    updatedItems[index] = {
      ...updatedItems[index],
      isEnabledForAIOperator: checked
    };
    onChange(updatedItems);
  };

  const onDeleteItem = (index: number) => () => {
    const updatedItems = [...sopNotes];
    updatedItems.splice(index, 1);
    onChange(updatedItems);
  };

  const onAddItem = () => {
    const updatedItems = [...sopNotes];
    updatedItems.push({
      text: '',
      isEnabledForAIOperator: false
    });
    onChange(updatedItems);
  };

  return (
    <div className="col-span-2 space-y-4">
      {sopNotes.map((item, i) => (
        <div key={i} className="flex items-center gap-4">
          <div className="flex-1">
            <InputField 
              value={item.text} 
              type="text" 
              onChange={onInputChange(i)}
              placeholder="Enter note text..."
            />
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              checked={item.isEnabledForAIOperator}
              onChange={onCheckboxChange(i)}
            />
            <span className="text-sm text-onlight-text-2 dark:text-ondark-text-2 whitespace-nowrap">
              AI Operator
            </span>
          </div>
          {showDeleteButtons && (
            <Button variant="icon" onClick={onDeleteItem(i)}>
              <TrashIcon className="h-5 w-5" />
            </Button>
          )}
        </div>
      ))}
      {showAddButton && (
        <div>
          <Button onClick={onAddItem}>Add note</Button>
        </div>
      )}
    </div>
  );
}
