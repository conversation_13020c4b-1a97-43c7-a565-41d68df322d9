export enum SOPTalkdownType {
  STATIC = 'static',
  DYANMIC = 'dynamic',
}

export type SOPTalkdown = {
  type: SOPTalkdownType;
  text: string;
};

export enum SituationColor {
  RED = 'red',
  GREEN = 'green',
  BLUE = 'blue',
}
export interface Situation {
  label: string;
  color: SituationColor;
}
export interface CameraLevelNote {
  cameraId: string;
  cameraName: string;
  notes: string[];
}

export interface SOPNote {
  text: string;
  isEnabledForAIOperator: boolean;
}

export type SOPWorkflow = {
  firstTalkdown?: SOPTalkdown;
  secondTalkdown?: SOPTalkdown;
  exceptions: string[];
  siteAddress: string;
  siteGoogleMapLocation: string;
  siteCategory?: string;
  isTalkdownEnabled: boolean;
  escalationPoints: string[];
  notes: string[] | SOPNote[];
  aiOperatorNotes?: string[];
  talkdownNotes?: string[];
  situations: Situation[];
  escalationProtocol: string[];
  quickResolveActions?: string[];
  isZeroTolerance?: boolean;
  talkdowns?: SOPTalkdown[];
  cameraLevelNotes?: CameraLevelNote[];
};

export type ScanSOPContact = {
  name: string;
  phoneNumber: string;
  role?: string;
};

export type ScanTenantSOP = {
  address: string;
  emergencySituations: string[];
  nonEmergencySituations: string[];
  exceptions: string[];
  emergencyContact: ScanSOPContact[];
  nonEmergencyContact: ScanSOPContact[];
  isTalkdownEnabled?: boolean;
};

export interface SOPDTO {
  id?: string;
  sop_workflow?: SOPWorkflow;
  scan_sop?: ScanTenantSOP;
}

export interface UpdateSOPPayload {
  sop_text: string;
}

export interface CreateSOPPayload {
  sop_text: string;
  tenant_id: string;
  location_id: string;
}

export interface SOPWorkflowState {
  logCallFromSOP: (twilioCallSid: string, toName: string) => void;
}
